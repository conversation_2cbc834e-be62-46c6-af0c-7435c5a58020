block result : text!
========

========

block test1_input : _lexer_input!
========
1 $ 1
========

block test1_result : _lexer_expected!
=====================
+Number+
1
+BinaryOperator+
$
+Number+
1
=====================

block error_test : _lexer_input !
========
人 a
========

block error_test_result : _lexer_expected !
========
+Identifier+
a
========

block error_test_error1 : _lexer_error_state !
========
1:1
unexpected_character
lexer_inject
========
