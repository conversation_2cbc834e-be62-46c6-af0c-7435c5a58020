#ifndef SKIPLIST_H
#define SKIPLIST_H
#include <cassert>
#include <cstdlib>
#include <climits>

constexpr int ElemLog2(unsigned long long d)
{
    int res = sizeof(d) * 8;
    while (res > 0)
    {
        res--;
        if (d & (1ULL << (sizeof(d) * 8 - 1)))
            break;
        d <<= 1;
    }
    res >>= 1;
    return res;
}
constexpr int operator"" _Elem(unsigned long long d)
{
    int res = ElemLog2(d);
    if (res <= 0)
        return 1;
    return res;
}
constexpr int operator"" _KElem(unsigned long long d)
{
    int res = ElemLog2(d) + 5;
    if (res <= 0)
        return 1;
    return res;
}
constexpr int operator"" _MElem(unsigned long long d)
{
    int res = ElemLog2(d) + 10;
    if (res <= 0)
        return 1;
    return res;
}
constexpr int operator"" _GElem(unsigned long long d)
{
    int res = ElemLog2(d) + 15;
    if (res <= 0)
        return 1;
    return res;
}

inline unsigned int lowestBit(unsigned int v)
{
    unsigned int res   = 0;
    int          count = sizeof(int) * 8 - 1;
    if (v == 0)
        return 0;
    while (count-- > 0)
    {
        if (v & 1)
            break;
        v >>= 1;
        res++;
    }
    return res;
}

inline unsigned int randomLevel()
{
    return lowestBit(std::rand()) >> 1;
}
inline unsigned int indexLevel(unsigned int i)
{
    return lowestBit(i) >> 1;
}

#include <stdexcept>
template <class T, class I>
struct SkipListLeaf;
template <class T, class I, int MaxDepth>
class SkipList;

template <class T, class I>
struct SkipListNode
{
    I                    index;
    struct SkipListNode *next, *prev;
    union
    {
        struct SkipListNode       *n;
        struct SkipListLeaf<T, I> *f;
    } down;
    struct SkipListNode *up;
    int                  count;
    int                  level;
    void                 makeNode(SkipListLeaf<T, I> *leaf, I i, I i_sum, int lvl, int n)
    {
        if (lvl < level)
        {
            updateNodeCount(1, i);
            return;
        }
        SkipListNode *node = new SkipListNode;
        leaf->up           = node;
        node->next         = next;
        node->prev         = this;
        node->down.f       = leaf;
        node->up           = nullptr;
        node->index        = index - i_sum + i;
        node->count        = count - n + 1;
        node->level        = level;
        next->prev         = node;
        next               = node;
        count              = n;
        index              = i_sum;
        SkipListNode *u    = findUpNode(&n, &i_sum);
        if (u)
            u->makeNode(node, i, i_sum, lvl, n);
    }
    void makeNode(SkipListNode *node, I i, I i_sum, int lvl, int n)
    {
        if (lvl < level)
        {
            updateNodeCount(1, i);
            return;
        }
        SkipListNode *newNode = new SkipListNode;
        newNode->next         = next;
        newNode->prev         = this;
        newNode->down.n       = node;
        newNode->up           = nullptr;
        newNode->index        = index - i_sum + i;
        newNode->count        = count - n + 1;
        newNode->level        = level;
        node->up              = newNode;
        next->prev            = newNode;
        next                  = newNode;
        count                 = n;
        index                 = i_sum;
        SkipListNode *u       = findUpNode(&n, &i_sum);
        if (u)
            u->makeNode(newNode, i, i_sum, lvl, n);
    }
    void removeNode(const I &diff)
    {
        prev->next = next;
        if (next)
            next->prev = prev;
        prev->count += count - 1;
        prev->index += index - diff;
        if (up)
            up->removeNode(diff);
        else
        {
            SkipListNode *n = findUpNode();
            if (n)
                n->updateNodeCount(-1, -diff);
        }
    }
    void updateNodeCount(int rel, const I &diff)
    {
        count += rel;
        index += diff;
        SkipListNode *n = findUpNode();
        if (n)
            n->updateNodeCount(rel, diff);
    }
    static SkipListNode *trySeek(SkipListNode *start, int *n)
    {
        while (1)
        {
            if (start->up)
                start = start->up->trySeek(start->up, n)->down.n;
            if (*n >= start->count)
            {
                *n -= start->count;
                start = start->next;
            }
            else if (*n < 0)
            {
                start = start->prev;
                *n += start->count;
            }
            else
                return start;
            if (!start)
                throw std::out_of_range("SkipList: index out of range");
        }
    }
    static SkipListNode *trySeekI(SkipListNode *start, I *index)
    {
        while (1)
        {
            if (start->up)
                start = start->up->trySeekI(start->up, index)->down.n;
            else if (*index >= start->index)
            {
                *index -= start->index;
                start = start->next;
            }
            else if (*index < 0)
            {
                *index += start->index;
                start = start->prev;
            }
            else
                return start;
            if (!start)
                throw std::out_of_range("SkipList: index out of range");
        }
    }
    SkipListLeaf<T, I> *first() const
    {
        if (level <= 1)
            return down.f;
        return down.n->first();
    }
    SkipListNode *findUpNode(int *n = nullptr, I *i = nullptr)
    {
        SkipListNode *p = this;
        while (!p->up)
        {
            p = p->prev;
            if (!p)
                return nullptr;
            if (n)
                *n += p->count;
            if (i)
                *i += p->index;
        }
        return p->up;
    }
    I sum()
    {
        I             res = index;
        SkipListNode *p   = this;
        while (!p->up)
        {
            if (p->prev)
            {
                p = p->prev;
                res += p->index;
            }
            else
                return res;
        }
        if (p->up->prev)
            return p->up->prev->sum() + res;
        else
            return res;
    }
};

template <class T>
struct SkipListNode<T, void>
{
    struct SkipListNode *next, *prev;
    union
    {
        struct SkipListNode          *n;
        struct SkipListLeaf<T, void> *f;
    } down;
    struct SkipListNode   *up;
    int                    count;
    int                    level;
    void                   makeNode(SkipListLeaf<T, void> *leaf, int lvl, int n);
    void                   makeNode(SkipListNode *node, int lvl, int n);
    void                   removeNode();
    void                   updateNodeCount(int rel);
    static SkipListNode   *trySeek(SkipListNode *start, int *n);
    SkipListLeaf<T, void> *first() const;
    SkipListNode          *findUpNode(int *n = nullptr);
};

template <class T, class I>
struct SkipListLeaf:public T
{
protected:
    SkipListLeaf(SkipListLeaf *next, SkipListLeaf *prev, SkipListNode<T, I> *up, const T &value,
                 const I &index)
        : next(next)
        , prev(prev)
        , up(up)
        , index(index)
        , T(value)
    {
    }
    SkipListLeaf()
    {
    }
    struct SkipListLeaf       *next, *prev;
    struct SkipListNode<T, I> *up;
    I                          index;
    struct SkipListNode<T, I> *findNode(int *n = nullptr, I *i = nullptr)
    {
        SkipListLeaf *p = this;
        while (!p->up)
        {
            p = p->prev;
            if (n)
                (*n)++;
            if (i)
                *i += p->index;
            assert(p);
        }
        return p->up;
    }


public:
//    T    value;
    bool isVaild() const
    {
        return prev && next;
    }
    void setIndex(const I &i)
    {
        I diff = i - index;
        findNode()->updateNodeCount(0, diff);
    }
    I getIndex() const
    {
        return index;
    }
    I sum()
    {
        I             res = index;
        SkipListLeaf *p   = this;
        while (!p->up)
        {
            assert(p->prev);
            p = p->prev;
            res += p->index;
        }
        if (p->up->prev)
            return p->up->prev->sum() + res;
        else
            return res;
    }
    static std::pair<SkipListLeaf *, I> seekI(SkipListLeaf *start, I i)
    {
        while (i < I() || i > start->index)
        {
            if (start->up)
                start = start->up->trySeekI(start->up, &i)->first();
            if (i > start->index)
            {
                start = start->next;
                i -= start->index;
            }
            else if (i < I())
            {
                i += start->index;
                start = start->prev;
            }
        }
        return std::make_pair(start, i);
    }
    static SkipListLeaf *seek(SkipListLeaf *start, int n)
    {
        bool trySeekFlag = n > 20;
        while (n)
        {
            if (trySeekFlag && start->up)
            {
                start       = start->up->trySeek(start->up, &n)->first();
                trySeekFlag = false;
            }
            if (n > 0)
            {
                start = start->next;
                n--;
            }
            else if (n < 0)
            {
                start = start->prev;
                n++;
            }
        }
        return start;
    }
    SkipListLeaf *
    insertNext(const T &v, const I &i, int lvl = -1)
    {
        if (lvl < 0)
            lvl = randomLevel();
        auto d                       = new SkipListLeaf(next, this, nullptr, v, i);
        next->prev                   = d;
        next                         = d;
        int                        n = 1;
        I                          sum{index};
        struct SkipListNode<T, I> *node = findNode(&n, &sum);
        if (lvl)
        {
            node->makeNode(d, i, sum, lvl, n);
        }
        else
        {
            node->updateNodeCount(1, i);
        }
        return d;
    }
    SkipListLeaf *remove()
    {
        if (!prev || !next)
        {
            throw std::out_of_range("Skip list: index out of range");
        }
        next->prev = prev;
        prev->next = next;
        if (up)
            up->removeNode(index);
        else
            findNode()->updateNodeCount(-1, -index);
        auto n = next;
        delete this;
        return n;
    }
    SkipListLeaf *take()
    {
        if (!prev || !next)
        {
            throw std::out_of_range("Skip list: index out of range");
        }
        next->prev = prev;
        prev->next = next;
        if (up)
            up->removeNode(index);
        else
            findNode()->updateNodeCount(-1, -index);
		prev = next = nullptr;
		return this;
    }
    SkipListLeaf *getNext() const
    {
        return next;
    }
    SkipListLeaf *getPrev() const
    {
        return prev;
    }
    SkipListNode<T, I> *getUp() const
    {
        return up;
    }
    template <typename T_, typename I_, int maxDepth>
    friend class SkipList;
    template <class T_, class I_>
    friend class SkipListIterator;
    template <class T_, class I_>
    friend struct SkipListNode;
};
template <class T>
struct SkipListLeaf<T, void>:public T
{
protected:
    SkipListLeaf(SkipListLeaf *next, SkipListLeaf *prev, SkipListNode<T, void> *up, const T &value)
        : next(next)
        , prev(prev)
        , up(up)
        , T(value)
    {
    }
    SkipListLeaf()
    {
    }
    struct SkipListLeaf          *next, *prev;
    struct SkipListNode<T, void> *up;
    struct SkipListNode<T, void> *findNode(int *n = nullptr);

public:
    T                    value;
    bool                 isVaild() const;
    static SkipListLeaf *seek(SkipListLeaf *start, int n);
    SkipListLeaf        *insertNext(const T &v, int lvl = -1);
    SkipListLeaf        *remove();
    SkipListLeaf        *take();

    SkipListLeaf        *getNext() const
    {
        return next;
    }
    SkipListLeaf *getPrev() const
    {
        return prev;
    }
    SkipListNode<T, void> *getUp() const
    {
        return up;
    }
    template <typename T_, typename I_, int maxDepth>
    friend class SkipList;
    template <class T_, class I_>
    friend class SkipListIterator;
    template <class T_, class I_>
    friend struct SkipListNode;
};


template <class T, class I = void>
class SkipListIterator
{
public:
    SkipListIterator()
        : node(nullptr)
    {
    }
    SkipListIterator(SkipListLeaf<T, I> *n)
        : node(n)
    {
    }
    SkipListIterator(const SkipListIterator &i) = default;
    SkipListLeaf<T, I> &operator=(const SkipListLeaf<T, I> &n)
    {
        node = n;
    }
    SkipListIterator<T, I> &operator=(const SkipListIterator<T, I> &n) = default;
    bool isNull() const
    {
		return node == nullptr;
	}
    T &operator*()
    {
        return node->value;
    }
    T *operator->()
    {
        return static_cast<T*>(node);
    }
    const T &operator*() const
    {
        return node->value;
    }
    const T *operator->() const
    {
        return &node->value;
    }
    SkipListIterator operator+(int n) const
    {
        return SkipListLeaf<T, I>::seek(node, n);
    }
    SkipListIterator operator-(int n) const
    {
        return SkipListLeaf<T, I>::seek(node, -n);
    }
    int pos() const
    {
        int                 res  = 0;
        SkipListLeaf<T, I> *leaf = node;
        while (leaf->up == nullptr)
        {
            res++;
            leaf = leaf->prev;
        }
        SkipListNode<T, I> *p = leaf->up;
        while (1)
        {
            if (p->up)
                p = p->up;
            else if (p->prev)
            {
                p = p->prev;
                res += p->count;
            }
            else
            {
                break;
            }
        }
        return res-1;
    }
    SkipListIterator &operator++()
    {
        node = node->next;
        return *this;
    }
    SkipListIterator &operator++(int)
    {
        node = node->next;
        return *this;
    }
    SkipListIterator &operator--()
    {
        node = node->prev;
        return *this;
    }
    SkipListIterator &operator--(int)
    {
        node = node->prev;
        return *this;
    }
    SkipListIterator remove()
    {
        return node->remove();
    }
    void take()
    {
        node->take();
    }

    //    int operator-(const SkipListIterator& n)const{

    //    }
    int refCount() const
    {
        assert(node);
        if (!node->up)
            return 0;
        int  cnt = 1;
        auto p   = node->up;
        while (p)
        {
            cnt++;
            p = p->up;
        }
        return cnt;
    }
    bool operator!=(const SkipListIterator &d)
    {
        return node != d.node;
    }
    bool operator==(const SkipListIterator &d)
    {
        return node == d.node;
    }
    SkipListLeaf<T, I> *getPtr() const
    {
        return node;
    }
    I index() const
    {
        return node->index;
    }

private:
    SkipListLeaf<T, I> *node;
};

template <class T, class I, int MaxDepth>
class SkipList
{
public:
    using iterator = SkipListIterator<T, I>;
    explicit SkipList();
    ~SkipList();
    void     clear();
    const T &at(unsigned int n) const;
    //    const T &at(const I &index) const;
    T &operator[](unsigned int n);
    //    T       &operator[](const I &index);
    iterator append(const T &value, const I &index, int lvlMask = -1);
    iterator prepend(const T &value, const I &index, int lvlMask = -1);
    int      count() const;
    I        sum() const;
    iterator insert(int i, const I &index, const T &value)
    {
        //        if (i == c)
        //            return append(value);
        return findN(i)->prev->insertNext(value, index);
    }
    iterator remove(int i, int count=1)
    {
        assert(count >= 0);
        auto p = findN(i);
        while (count--)
            p = p->remove();
        return p;
    }
    SkipListIterator<T, I> begin() const
    {
        return SkipListIterator<T, I>(const_cast<SkipListLeaf<T,I>*>(first.next));
    }
    SkipListIterator<T, I> end() const
    {
        return SkipListIterator<T, I>(const_cast<SkipListLeaf<T,I>*>(&last));
    }
    std::pair<SkipListLeaf<T, I> *, I> findI(I index);

    SkipListLeaf<T, I> *nodeBeforeBegin();
    bool                indexCheck(bool rebuild = false);
    bool                   isEmpty() const
    {
        return first.next == &last;
    }

private:
    void                freeNodes(const SkipListNode<T, I> *n);
    void                freeLeafs(const SkipListLeaf<T, I> *n);
    SkipListLeaf<T, I> *findN(int n)const;
    void                initList();
    SkipListNode<T, I>  topFirst[MaxDepth];
    SkipListNode<T, I>  topLast[MaxDepth];
    SkipListLeaf<T, I>  first, last;
};
template <class T, int MaxDepth>
class SkipList<T, void, MaxDepth>
{
public:
    using iterator = SkipListIterator<T, void>;
    explicit SkipList();
    ~SkipList();
    void     clear();
    const T &at(unsigned int n) const;
    T       &operator[](unsigned int n);
    iterator append(const T &value, int lvlMask = -1);
    iterator prepend(const T &value, int lvlMask = -1);
    int      count() const;
    iterator insert(int i, const T &value)
    {
        //        if (i == c)
        //            return append(value);
        return findN(i)->prev->insertNext(value);
    }
    iterator remove(int i, int count=1)
    {
        assert(count >= 0);
        auto p = findN(i);
        while (count--)
            p = p->remove();
        return p;
    }
    SkipListIterator<T, void> begin() const
    {
        return SkipListIterator<T, void>(const_cast<SkipListLeaf<T,void>*>(first.next));
    }
    SkipListIterator<T, void> end() const
    {
        return SkipListIterator<T, void>(const_cast<SkipListLeaf<T,void>*>(&last));
    }
    T &firstElem() const
    {
        return first.next->value;
    }
    T &lastElem() const
    {
        return last.prev->value;
    }

    SkipListLeaf<T, void> *nodeBeforeBegin();
    bool                   indexCheck(bool rebuild = false);
    bool                   isEmpty() const
    {
        return first.next == &last;
    }
private:
    void                   freeNodes(const SkipListNode<T, void> *n);
    void                   freeLeafs(const SkipListLeaf<T, void> *n);
    SkipListLeaf<T, void> *findN(int n)const;
    void                   initList();
    SkipListNode<T, void>  topFirst[MaxDepth];
    SkipListNode<T, void>  topLast[MaxDepth];
    SkipListLeaf<T, void>  first, last;
};

template <class T, int MaxDepth>
SkipList<T, void, MaxDepth>::SkipList()
{
    initList();
}
template <class T, class I, int MaxDepth>
SkipList<T, I, MaxDepth>::SkipList()
{
    initList();
}

template <class T, int MaxDepth>
SkipList<T, void, MaxDepth>::~SkipList()
{
    clear();
}
template <class T, class I, int MaxDepth>
SkipList<T, I, MaxDepth>::~SkipList()
{
    clear();
}

template <class T, int MaxDepth>
void SkipList<T, void, MaxDepth>::clear()
{
    for (SkipListNode<T, void> &n : topFirst)
    {
        freeNodes(&n);
        n.count = 1;
    }
    freeLeafs(&first);
    initList();
}
template <class T, class I, int MaxDepth>
void SkipList<T, I, MaxDepth>::clear()
{
    for (SkipListNode<T, I> &n : topFirst)
    {
        freeNodes(&n);
        n.count = 1;
    }
    freeLeafs(&first);
    initList();
}

template <class T, int MaxDepth>
const T &SkipList<T, void, MaxDepth>::at(unsigned int n) const
{
    //        if (!top.isEmpty())
    //            throw std::out_of_range("EMPTY skip list");
    auto p = findN(n);
    return p->value;
}
template <class T, class I, int MaxDepth>
const T &SkipList<T, I, MaxDepth>::at(unsigned int n) const
{
    //        if (!top.isEmpty())
    //            throw std::out_of_range("EMPTY skip list");
    auto p = findN(n);
    return p->value;
}
// template <class T, class I, int MaxDepth>
// const T &SkipList<T, I, MaxDepth>::at(const I &index) const
//{
//     //        if (!top.isEmpty())
//     //            throw std::out_of_range("EMPTY skip list");
//     auto p = findI(index).first;
//     return p->value;
// }

template <class T, int MaxDepth>
T &SkipList<T, void, MaxDepth>::operator[](unsigned int n)
{
    //        if (!top.isEmpty())
    //            throw std::out_of_range("EMPTY skip list");
    auto p = findN(n);
    return p->value;
}
// template <class T, class I, int MaxDepth>
// T &SkipList<T, I, MaxDepth>::operator[](const I &index)
//{
//     //        if (!top.isEmpty())
//     //            throw std::out_of_range("EMPTY skip list");
//     auto p = findI(index).first;
//     return p->value;
// }
template <class T, class I, int MaxDepth>
T &SkipList<T, I, MaxDepth>::operator[](unsigned int n)
{
    //        if (!top.isEmpty())
    //            throw std::out_of_range("EMPTY skip list");
    auto p = findN(n);
    return p->value;
}

template <class T, int MaxDepth>
typename SkipList<T, void, MaxDepth>::iterator SkipList<T, void, MaxDepth>::prepend(const T &value,
                                                                                   int      lvlMask)
{
    if (lvlMask < 0)
        return first.insertNext(value, -1);
    else
        return first.insertNext(value, indexLevel(count()));
}
template <class T, class I, int MaxDepth>
typename SkipList<T, I, MaxDepth>::iterator
SkipList<T, I, MaxDepth>::prepend(const T &value, const I &index, int lvlMask)
{
    if (lvlMask < 0)
        return first->insertNext(value, index, -1);
    else
        return first->insertNext(value, index, indexLevel(count()));
}

template <class T, int MaxDepth>
typename SkipList<T, void, MaxDepth>::iterator SkipList<T, void, MaxDepth>::append(const T &value,
                                                                                   int      lvlMask)
{
    if (lvlMask < 0)
        return last.prev->insertNext(value, -1);
    else
        return last.prev->insertNext(value, indexLevel(count()));
}
template <class T, class I, int MaxDepth>
typename SkipList<T, I, MaxDepth>::iterator
SkipList<T, I, MaxDepth>::append(const T &value, const I &index, int lvlMask)
{
    if (lvlMask < 0)
        return last.prev->insertNext(value, index, -1);
    else
        return last.prev->insertNext(value, index, indexLevel(count()));
}

template <class T, int MaxDepth>
int SkipList<T, void, MaxDepth>::count() const
{
    int                          sum  = 0;
    const SkipListNode<T, void> *node = topFirst + MaxDepth - 1;
    while (node)
    {
        sum += node->count;
        node = node->next;
    }
    return sum - 2;
}
template <class T, class I, int MaxDepth>
int SkipList<T, I, MaxDepth>::count() const
{
    int                       sum  = 0;
    const SkipListNode<T, I> *node = topFirst + MaxDepth - 1;
    while (node)
    {
        sum += node->count;
        node = node->next;
    }
    return sum - 2;
}

template <class T, class I, int MaxDepth>
I SkipList<T, I, MaxDepth>::sum() const
{
    I                         sum  = I();
    const SkipListNode<T, I> *node = topFirst + MaxDepth - 1;
    while (node)
    {
        sum += node->index;
        node = node->next;
    }
    return sum;
}


template <class T, int MaxDepth>
SkipListLeaf<T, void> *SkipList<T, void, MaxDepth>::nodeBeforeBegin()
{
    return &first;
}
template <class T, class I, int MaxDepth>
SkipListLeaf<T, I> *SkipList<T, I, MaxDepth>::nodeBeforeBegin()
{
    return &first;
}

template <class T, int MaxDepth>
bool SkipList<T, void, MaxDepth>::indexCheck(bool rebuild)
{
    int                    cnt  = 1;
    SkipListLeaf<T, void> *p    = first.next;
    SkipListNode<T, void> *node = topFirst, *node2;
    while (p)
    {
        if (p->up)
        {
            if (node->count != cnt)
            {
                if (!rebuild)
                    return false;
                else
                    node->count = cnt;
            }
            cnt = 1;
            assert(node->next == p->up);
            node = node->next;
        }
        else
            cnt++;
        p = p->next;
    }
    for (int i = 0; i < MaxDepth - 1; i++)
    {
        node  = topFirst + i;
        node2 = topFirst + i + 1;
        cnt   = node->count;
        node  = node->next;
        if (node->up)
        {
            if (node2->count != cnt)
            {
                if (!rebuild)
                    return false;
                else
                    node2->count = cnt;
            }
            cnt = node->count;
            assert(node2->next == node->up);
            node2 = node2->next;
        }
        else
            cnt += node->count;
        node = node->next;
    }
    return true;
}
template <class T, class I, int MaxDepth>
bool SkipList<T, I, MaxDepth>::indexCheck(bool rebuild)
{
    int                 cnt = 1;
    I                   index{};
    SkipListLeaf<T, I> *p    = first.next;
    SkipListNode<T, I> *node = topFirst, *node2;
    while (p)
    {
        if (p->up)
        {
            if (node->count != cnt)
            {
                if (!rebuild)
                    return false;
                else
                    node->count = cnt;
            }
            if (node->index != index)
            {
                if (!rebuild)
                    return false;
                else
                    node->index = index;
            }
            cnt   = 1;
            index = p->index;
            assert(node->next == p->up);
            node = node->next;
        }
        else
        {
            cnt++;
            index += p->index;
        }
        p = p->next;
    }
    for (int i = 0; i < MaxDepth - 1; i++)
    {
        node  = topFirst + i;
        node2 = topFirst + i + 1;
        cnt   = node->count;
        index = node->index;
        node  = node->next;
        if (node->up)
        {
            if (node2->count != cnt)
            {
                if (!rebuild)
                    return false;
                else
                    node2->count = cnt;
            }
            if (node2->index != index)
            {
                if (!rebuild)
                    return false;
                else
                    node2->index = index;
            }
            cnt   = node->count;
            index = node->index;
            assert(node2->next == node->up);
            node2 = node2->next;
        }
        else
            cnt += node->count;
        node = node->next;
    }
    return true;
}

template <class T, int MaxDepth>
void SkipList<T, void, MaxDepth>::freeNodes(const SkipListNode<T, void> *n)
{
    while (n)
    {
        auto next = n->next;
        if (n->prev && n->next)
            delete n;
        n = next;
    }
}
template <class T, class I, int MaxDepth>
void SkipList<T, I, MaxDepth>::freeNodes(const SkipListNode<T, I> *n)
{
    while (n)
    {
        auto next = n->next;
        if (n->prev && n->next)
            delete n;
        n = next;
    }
}

template <class T, int MaxDepth>
void SkipList<T, void, MaxDepth>::freeLeafs(const SkipListLeaf<T, void> *n)
{
    while (n)
    {
        auto next = n->next;
        if (n->prev && n->next)
            delete n;
        n = next;
    }
}
template <class T, class I, int MaxDepth>
void SkipList<T, I, MaxDepth>::freeLeafs(const SkipListLeaf<T, I> *n)
{
    while (n)
    {
        auto next = n->next;
        if (n->prev && n->next)
            delete n;
        n = next;
    }
}

template <class T, int MaxDepth>
SkipListLeaf<T, void> *SkipList<T, void, MaxDepth>::findN(int n) const
{
    if (n < 0)
        throw std::out_of_range("Skip list: index out of range");
    n++;
    const SkipListNode<T, void> *p = topFirst + MaxDepth - 1;
    while (1)
    {
        if (n < p->count)
        {
            p = p->down.n;
            if (p->level == 1)
                break;
        }
        else
        {
            n -= p->count;
            p = p->next;
        }
        if (!p)
            throw std::out_of_range("Skip list: index out of range");
    }
    assert(n >= 0);
    auto l = p->down.f;
    while (n--)
        l = l->next;
    return l;
}
template <class T, class I, int MaxDepth>
SkipListLeaf<T, I> *SkipList<T, I, MaxDepth>::findN(int n) const
{
    if (n < 0)
        throw std::out_of_range("Skip list: index out of range");
    n++;
    const SkipListNode<T, I> *p = topFirst + MaxDepth - 1;
    while (1)
    {
        if (n < p->count)
        {
            p = p->down.n;
            if (p->level == 1)
                break;
        }
        else
        {
            n -= p->count;
            p = p->next;
        }
        if (!p)
            throw std::out_of_range("Skip list: index out of range");
    }
    assert(n >= 0);
    auto l = p->down.f;
    while (n--)
        l = l->next;
    return l;
}
template <class T, class I, int MaxDepth>
std::pair<SkipListLeaf<T, I> *, I> SkipList<T, I, MaxDepth>::findI(I index)
{
    if (index < I())
        throw std::out_of_range("Skip list: index out of range");
    const SkipListNode<T, I> *p = topFirst + MaxDepth - 1;
    while (1)
    {
        if (index < p->index)
        {
            p = p->down.n;
            if (p->level == 1)
                break;
        }
        else
        {
            index -= p->index;
            p = p->next;
        }
        if (!p)
            return std::make_pair(&last, index);
    }
    assert(index >= 0);
    auto l = p->down.f;
    while (index > l->index)
    {
        index -= l->index;
        l = l->next;
    }
    if (l->prev)
        return std::make_pair(l, index);
    return std::make_pair(l->next, index);
}

template <class T, class I, int MaxDepth>
void SkipList<T, I, MaxDepth>::initList()
{
    first.prev               = nullptr;
    first.next               = &last;
    first.up                 = topFirst + 0;
    last.prev                = &first;
    last.next                = nullptr;
    last.up                  = topLast + 0;
    first.index              = I();
    last.index               = I();
    SkipListNode<T, I> *down = nullptr;
    topFirst[0].next         = topLast + 0;
    topFirst[0].prev         = nullptr;
    topFirst[0].down.f       = &first;
    topFirst[0].level        = 1;
    topFirst[0].count        = 1;
    topFirst[0].index        = I();
    down                     = topFirst + 0;
    for (int i = 1; i < MaxDepth; i++)
    {
        down->up           = topFirst + i;
        topFirst[i].down.n = down;
        topFirst[i].next   = topLast + i;
        topFirst[i].prev   = nullptr;
        topFirst[i].level  = i + 1;
        topFirst[i].count  = 1;
        topFirst[i].up     = nullptr;
        topFirst[i].index  = I();
        down               = topFirst + i;
    }
    topLast[0].next   = nullptr;
    topLast[0].prev   = topFirst + 0;
    topLast[0].down.f = &last;
    topLast[0].level  = 1;
    topLast[0].count  = 1;
    topLast[0].index  = I();
    down              = topLast + 0;
    for (int i = 1; i < MaxDepth; i++)
    {
        down->up          = topLast + i;
        topLast[i].down.n = down;
        topLast[i].next   = nullptr;
        topLast[i].prev   = topFirst + i;
        topLast[i].level  = i + 1;
        topLast[i].count  = 1;
        topLast[i].up     = nullptr;
        topLast[i].index  = I();
        down              = topLast + i;
    }
}
template <class T, int MaxDepth>
void SkipList<T, void, MaxDepth>::initList()
{
    first.prev                  = nullptr;
    first.next                  = &last;
    first.up                    = topFirst + 0;
    last.prev                   = &first;
    last.next                   = nullptr;
    last.up                     = topLast + 0;
    first.value                 = T();
    last.value                  = T();
    SkipListNode<T, void> *down = nullptr;
    topFirst[0].next            = topLast + 0;
    topFirst[0].prev            = nullptr;
    topFirst[0].down.f          = &first;
    topFirst[0].level           = 1;
    topFirst[0].count           = 1;
    down                        = topFirst + 0;
    for (int i = 1; i < MaxDepth; i++)
    {
        down->up           = topFirst + i;
        topFirst[i].down.n = down;
        topFirst[i].next   = topLast + i;
        topFirst[i].prev   = nullptr;
        topFirst[i].level  = i + 1;
        topFirst[i].count  = 1;
        topFirst[i].up     = nullptr;
        down               = topFirst + i;
    }
    topLast[0].next   = nullptr;
    topLast[0].prev   = topFirst + 0;
    topLast[0].down.f = &last;
    topLast[0].level  = 1;
    topLast[0].count  = 1;
    down              = topLast + 0;
    for (int i = 1; i < MaxDepth; i++)
    {
        down->up          = topLast + i;
        topLast[i].down.n = down;
        topLast[i].next   = nullptr;
        topLast[i].prev   = topFirst + i;
        topLast[i].level  = i + 1;
        topLast[i].count  = 1;
        topLast[i].up     = nullptr;
        down              = topLast + i;
    }
}



template <class T>
void SkipListNode<T, void>::makeNode(SkipListLeaf<T, void> *leaf, int lvl, int n) // count ++;
{
    if (lvl < level)
    {
        updateNodeCount(1);
        return;
    }
    SkipListNode *node = new SkipListNode;
    leaf->up           = node;
    node->next         = next;
    node->prev         = this;
    node->down.f       = leaf;
    node->up           = nullptr;
    node->count        = count - n + 1;
    node->level        = level;
    next->prev         = node;
    next               = node;
    count              = n;
    SkipListNode *u    = findUpNode(&n);
    if (u)
        u->makeNode(node, lvl, n);
}

template <class T>
void SkipListNode<T, void>::makeNode(SkipListNode *node, int lvl, int n) // count ++;
{
    if (lvl < level)
    {
        updateNodeCount(1);
        return;
    }
    SkipListNode *newNode = new SkipListNode;
    newNode->next         = next;
    newNode->prev         = this;
    newNode->down.n       = node;
    newNode->up           = nullptr;
    newNode->count        = count - n + 1;
    newNode->level        = level;
    node->up              = newNode;
    next->prev            = newNode;
    next                  = newNode;
    count                 = n;
    SkipListNode *u       = findUpNode(&n);
    if (u)
        u->makeNode(newNode, lvl, n);
}

template <class T>
void SkipListNode<T, void>::removeNode() // count--;
{
    prev->next = next;
    if (next)
        next->prev = prev;
    prev->count += count - 1;
    if (up)
        up->removeNode();
    else
    {
        SkipListNode *n = findUpNode();
        if (n)
            n->updateNodeCount(-1);
    }
}

template <class T>
void SkipListNode<T, void>::updateNodeCount(int rel)
{
    count += rel;
    SkipListNode *n = findUpNode();
    if (n)
        n->updateNodeCount(rel);
}

template <class T>
SkipListNode<T, void> *SkipListNode<T, void>::trySeek(SkipListNode *start, int *n)
{
    bool trySeekFlag = true;
    while (1)
    {
        if (trySeekFlag && start->up)
        {
            start       = start->up->trySeek(start->up, n)->down.n;
            trySeekFlag = false;
        }
        if (*n >= start->count)
        {
            *n -= start->count;
            start = start->next;
        }
        else if (*n < 0)
        {
            start = start->prev;
            *n += start->count;
        }
        else
            return start;
        if (!start)
            throw std::out_of_range("SkipList: index out of range");
    }
}

template <class T>
SkipListLeaf<T, void> *SkipListNode<T, void>::first() const
{
    if (level <= 1)
        return down.f;
    return down.n->first();
}

template <class T>
SkipListNode<T, void> *SkipListNode<T, void>::findUpNode(int *n)
{
    SkipListNode *p = this;
    while (!p->up)
    {
        p = p->prev;
        if (!p)
            return nullptr;
        if (n)
            *n += p->count;
    }
    return p->up;
}


template <class T>
SkipListNode<T, void> *SkipListLeaf<T, void>::findNode(int *n)
{
    SkipListLeaf *p = this;
    while (!p->up)
    {
        p = p->prev;
        if (n)
            (*n)++;
        assert(p);
    }
    return p->up;
}

template <class T>
SkipListLeaf<T, void> *SkipListLeaf<T, void>::insertNext(const T &v, int lvl)
{
    if (lvl < 0)
        lvl = randomLevel();
    auto d                             = new SkipListLeaf(next, this, nullptr, v);
    next->prev                         = d;
    next                               = d;
    int                           n    = 1;
    struct SkipListNode<T, void> *node = findNode(&n);
    if (lvl)
    {
        node->makeNode(d, lvl, n);
    }
    else
    {
        node->updateNodeCount(1);
    }
    return d;
}

template <class T>
SkipListLeaf<T, void> *SkipListLeaf<T, void>::remove()
{
    if (!prev || !next)
    {
        throw std::out_of_range("Skip list: index out of range");
    }
    next->prev = prev;
    prev->next = next;
    if (up)
        up->removeNode();
    else
        findNode()->updateNodeCount(-1);
    auto n = next;
    delete this;
    return n;
}

template <class T>
SkipListLeaf<T, void> *SkipListLeaf<T, void>::take()
{
    if (!prev || !next)
    {
        throw std::out_of_range("Skip list: index out of range");
    }
    next->prev = prev;
    prev->next = next;
    if (up)
        up->removeNode();
    else
        findNode()->updateNodeCount(-1);
	prev = next = nullptr;
	return this;
}

template<class T>
bool SkipListLeaf<T, void>::isVaild() const
{
    return prev && next;
}

template<class T>
SkipListLeaf<T, void> *SkipListLeaf<T, void>::seek(SkipListLeaf *start, int n)
{
    while (n)
    {
        if (start->up)
            start = start->up->trySeek(start->up, &n)->first();
        if (n > 0)
        {
            start = start->next;
            n--;
        }
        else if (n < 0)
        {
            start = start->prev;
            n++;
        }
    }
    return start;
}


#endif // SKIPLIST_H
