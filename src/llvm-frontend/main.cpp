#include <llvm/IR/LLVMContext.h>
#include <llvm/IR/Module.h>
#include <llvm/Support/raw_ostream.h>

int main() {
    llvm::LLVMContext context;
    auto module = std::make_unique<llvm::Module>("MyCompiler", context);
    
    // 创建函数原型: int main()
    llvm::FunctionType *funcType = 
        llvm::FunctionType::get(llvm::Type::getInt32Ty(context), false);
    llvm::Function::Create(funcType, 
                           llvm::Function::ExternalLinkage, 
                           "main", 
                           module.get());
    
    // 打印 IR
    module->print(llvm::outs(), nullptr);
    return 0;
}
