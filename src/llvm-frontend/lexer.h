#pragma once

#include <cassert>
#include <memory>
#include <optional>
#include <string>
#include <variant>
#include <vector>
#include <list>
#include <cstdint>
#include <uchar.h>
#include <unicode/uchar.h>
#include <unicode/utypes.h>

// 结构化错误类型定义
enum class LexerErrorType {
    UnexpectedCharacter,
    LexerInject,
    InvalidEscapeSequence,
    CharMustBeOneCharacter,
    RawIdentifierMustBeInOneLine,
    EnhancedBlockMustStartWithNewline,
    EnhancedBlockDelimiterMustBe3To120Characters,
    EnhancedBlockDelimiterMustConsistOfDashOrEqual,
    UnexpectedEnhancedBlockState,
    UnexpectedState,
    NestError,
    TokenizationRejectedByAllStates,
    TokenizationAcceptedByMultipleStates,
    MultipleStatesAfterFinalization,
    NoStateAfterFinalization,
    NotInNormalStateAfterFinalization
};

// 结构化错误信息
struct LexerError {
    LexerErrorType type;
    std::u32string message;
    int line;
    int column;

    LexerError(LexerErrorType t, const std::u32string& msg, int l = 0, int c = 0)
        : type(t), message(msg), line(l), column(c) {}
};

// 词法单元类型定义
enum class TokenType {
    Identifier, RawIdentifier, Number, String, Char, LineComment, BlockComment,
    BinaryOperator, PrefixOperator, SuffixOperator, Keyword, Punctuation, Whitespace, BlockContent,
    LeftParen, RightParen, LeftBracket, RightBracket, LeftBrace, RightBrace,
    // 其他类型...
};

struct Token : public std::enable_shared_from_this<Token> {
    TokenType type;
    std::u32string value;
    std::u32string nest_state;  // 括号嵌套状态
    std::shared_ptr<Token> prev;

    static inline std::shared_ptr<Token> make(TokenType type, const std::u32string& value, const std::u32string& nest = U"") {
        return std::make_shared<Token>(type, value, nest);
    }

    Token(TokenType type, const std::u32string& value, const std::u32string& nest = U"")
        : type(type), value(value), nest_state(nest) {}
};

struct SoueceInfo {
    int line;
    int column;
};

// 错误收集器
class LexerErrorCollector {
public:
    std::vector<LexerError> errors;
    std::vector<LexerError> warnings;

    void add_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info = nullptr);
    void add_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info = nullptr);
    bool has_errors() const { return !errors.empty(); }
    void clear() { errors.clear(); warnings.clear(); }
};

extern void generate_error(const std::u32string& message, const SoueceInfo* info);
extern void generate_warning(const std::u32string& message, const SoueceInfo* info);
extern void generate_structured_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info);
extern void generate_structured_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info);

// 状态变体类型
struct NormalState {
    bool rewind = false;
};
struct LineCommentState {
    bool wait_next_char = false;
};
struct BlockCommentState {
    enum class State {
        WaitNextChar, InComment, End
    } state = State::WaitNextChar;
};

struct StringState {
    std::u32string escape_text;
};

struct CharState {
    std::u32string escape_text;
};

struct RawIdentifierState {};

struct OperatorState {
    enum class Limit {
        NoPrefix, // `a$b`
        NoSuffix, // `a @op b` or `-a`
        BinaryOperatorOnly // `a @op b`
    };
    Limit limit = Limit::NoSuffix;
};

struct IdentifierState {};
struct NumberState {};

struct EnhancedBlockState {
    enum class State {
        WaitStart, Start, InContent
    } state = State::WaitStart;
    std::u32string delimiter;
};

struct ErrorState {
    LexerErrorType error_type;
    std::u32string error_message;

    ErrorState(LexerErrorType type, const std::u32string& message)
        : error_type(type), error_message(message) {}

    // 兼容性构造函数
    ErrorState(const std::u32string& message)
        : error_type(LexerErrorType::UnexpectedState), error_message(message) {}
};

// 括号嵌套状态管理
struct NestingState {
    std::u32string nest_stack;  // 括号栈，如 "([{"

    void push_bracket(char32_t bracket) {
        nest_stack += bracket;
    }

    bool pop_bracket(char32_t bracket) {
        if (nest_stack.empty()) return false;
        char32_t expected = get_matching_bracket(bracket);
        if (nest_stack.back() == expected) {
            nest_stack.pop_back();
            return true;
        }
        return false;
    }

    std::u32string get_nest_string() const {
        return nest_stack;
    }

private:
    char32_t get_matching_bracket(char32_t closing) {
        switch (closing) {
            case U')': return U'(';
            case U']': return U'[';
            case U'}': return U'{';
            default: return U'\0';
        }
    }
};

// 主状态类型定义
using StateVariant = std::variant<
    ErrorState, // 0
    NormalState, // 1
    LineCommentState,
    BlockCommentState,
    StringState,
    CharState,
    RawIdentifierState,
    OperatorState,
    IdentifierState,
    NumberState,
    EnhancedBlockState    
>;

// 引用计数支持的共享状态
struct LexerStateData {
    StateVariant state = NormalState{};
    std::u32string accumulated_value = U"";
    NestingState nesting_state = NestingState{};  // 括号嵌套状态

    static LexerStateData new_state(StateVariant state) {
        LexerStateData data;
        data.state = state;
        return data;
    }
    LexerStateData get_modified_state(StateVariant new_state)
    {
        assert(new_state.index() != 1);
        LexerStateData data = *this;
        data.state = new_state;
        return data;
    }
    std::u32string dump_state()const
    {
        std::u32string res;
        res += U"State: ";
        std::visit([&](auto&& arg) {
            using T = std::decay_t<decltype(arg)>;
            if constexpr (std::is_same_v<T, ErrorState>) {
                res += U"Error: ";
                res += arg.error_message;
            }else if constexpr (std::is_same_v<T, NormalState>) {
                res += U"Normal";
                if (arg.rewind) {
                    res += U" (rewind)";
                }
            }else if constexpr (std::is_same_v<T, LineCommentState>) {
                res += U"LineComment";
            }else if constexpr (std::is_same_v<T, BlockCommentState>) {
                res += U"BlockComment";
            }else if constexpr (std::is_same_v<T, StringState>) {
                res += U"String";
            }else if constexpr (std::is_same_v<T, CharState>) {
                res += U"Char";
            }else if constexpr (std::is_same_v<T, RawIdentifierState>) {
                res += U"RawIdentifier";
            }else if constexpr (std::is_same_v<T, OperatorState>) {
                res += U"Operator";
            }else if constexpr (std::is_same_v<T, IdentifierState>) {
                res += U"Identifier";
            }else if constexpr (std::is_same_v<T, NumberState>) {
                res += U"Number";
            }else if constexpr (std::is_same_v<T, EnhancedBlockState>) {
                res += U"EnhancedBlock";
            }
        }, state);
        res += U"\n";
        res += U"Accumulated value: ";
        res += accumulated_value;
        res += U"\n";
        res += U"Nesting state: ";
        res += nesting_state.get_nest_string();
        res += U"\n";
        return res;
    }
    // 其他全局状态信息...
};

class LexerState {
public:
    struct ExtendedState : public std::enable_shared_from_this<ExtendedState>{
        LexerStateData data = {NormalState{false}};
        std::shared_ptr<Token> token;
        std::shared_ptr<Token> last_token;
        std::shared_ptr<ExtendedState> prev;
    };
    LexerState():data({std::make_shared<ExtendedState>()}){}

    // 词法分析核心函数
    static std::pair<std::shared_ptr<Token>, std::vector<LexerStateData>> eat_char(LexerStateData state,char32_t c);

    LexerState eat_char(char32_t c, SoueceInfo *info)const
    {
        LexerState res;
        res.data.clear();
        for (auto& state : data) {
            if (state->data.state.index() == 0) continue; // Error state
            auto [token, next_states] = eat_char(state->data, c);
            for (auto &new_state : next_states) {
                auto new_extended_state = std::make_shared<ExtendedState>();
                new_extended_state->data = new_state;
                new_extended_state->token = token;
                new_extended_state->last_token = state->last_token;
                new_extended_state->prev = state->shared_from_this();
                if (token) {
                    token->prev = state->last_token;
                    new_extended_state->last_token = token;
                }
                res.data.push_back(new_extended_state);
            }
        }
        for (auto it = res.data.begin(); it != res.data.end();)
        {
            auto state = *it;
            if (const NormalState* normal_state = std::get_if<NormalState>(&state->data.state) ; normal_state && normal_state->rewind) {
                auto old_token = state->token;
                it = res.data.erase(it);
                auto [token, next_states] = eat_char(state->data, c);
                for (auto &new_state : next_states) {
                    auto new_extended_state = std::make_shared<ExtendedState>();
                    new_extended_state->data = new_state;
                    new_extended_state->last_token = state->last_token;
                    new_extended_state->prev = state->prev;
                    if (token) {
                        new_extended_state->token = token;
                        token->prev = state->last_token;
                        new_extended_state->last_token = token;
                    }else if (old_token) {
                        new_extended_state->token = old_token;
                        new_extended_state->last_token = old_token;
                    }else{
                        new_extended_state->token = nullptr;
                    }
                    res.data.push_back(new_extended_state);
                }
            }else it++;
        }
        bool all_error = true;
        bool has_error_state = false;
        for (auto const &ext_state : res.data) {
            if (ext_state->data.state.index() == 0) {
                // 这是ErrorState，报告其具体错误
                has_error_state = true;
                auto error_state = std::get<ErrorState>(ext_state->data.state);
                generate_structured_error(error_state.error_type, error_state.error_message, info);
            } else {
                all_error = false;
            }
        }
        if (all_error && !has_error_state) {
            generate_structured_error(LexerErrorType::TokenizationRejectedByAllStates, U"Tokenization error: Rejected by all states", info);
        }
        if (c == '\n'){
            if(res.data.size() == 0){
                generate_structured_error(LexerErrorType::TokenizationRejectedByAllStates, U"Tokenization error: Rejected by all states", info);
            }else if(res.data.size() > 1){
                generate_structured_warning(LexerErrorType::TokenizationAcceptedByMultipleStates, U"Tokenization warning: Accepted by multiple states", info);
            }
        }
        res.tokens = tokens;
        if (res.data.size() == 1) {
            auto state = res.data.front();
            auto new_token = state->last_token;
            std::list<std::shared_ptr<Token>> pendingTokens;
            auto token_list_end = std::shared_ptr<Token>(nullptr);
            if (tokens.size() > 0) {
                token_list_end = tokens.back();
            }
            if (new_token) {
                do{
                    if (new_token == token_list_end) break;
                    pendingTokens.push_back(new_token);
                    new_token = new_token->prev;
                }while(new_token);
            }
            for (auto& token : pendingTokens) {
                res.tokens.push_back(token);
            }
        }
        return res;
    }

    std::u32string dump_state()const
    {
        std::u32string res;
        for (auto& state : data) {
            res += state->data.dump_state();
        }
        return res;

    }

    LexerState finalize(SoueceInfo *info)const
    {
        if (data.size() ==1 && data.front()->data.state.index() == 1) {
            return *this;
        }
        LexerState res = eat_char(U'\n', info);
        if (res.data.size() != 1) {
            generate_structured_error(LexerErrorType::MultipleStatesAfterFinalization, U"Tokenization error: Multiple states after finalization", info);
        }else if (res.data.size() == 0) {
            generate_structured_error(LexerErrorType::NoStateAfterFinalization, U"Tokenization error: No state after finalization", info);
        }else if (res.data.front()->data.state.index() != 1) {
            generate_structured_error(LexerErrorType::NotInNormalStateAfterFinalization, U"Tokenization error: Not in normal state after finalization", info);
        }
        return res;
    }
    std::list<std::shared_ptr<Token>> get_tokens()const
    {
        return tokens;
    }
private:
    
    static bool is_other_operator(char32_t c);
    static bool is_prefix_operator(char32_t c);
    static bool is_operator(char32_t c);
    static bool is_name_start(char32_t c);
    static bool is_name_char(char32_t c);
    static bool is_delimiter_char(char32_t c);
    static bool is_whitespace(char32_t c);
    static bool is_digit(char32_t c);
    enum class EscapeCharState {
        InComplete, Complete, Error
    };
    static std::pair<EscapeCharState, char32_t> escape_char(const std::u32string& escape_text);
    static bool string_end_of(const std::u32string& data, const std::u32string& wanted);

    std::list<std::shared_ptr<ExtendedState>> data;
    std::list<std::shared_ptr<Token>> tokens;
};

inline std::pair<std::shared_ptr<Token>, std::vector<LexerStateData>> LexerState::eat_char(LexerStateData state,char32_t c) {
    std::vector<LexerStateData> next_states;
    std::shared_ptr<Token> token;
        
    std::visit([&](auto&& arg) {
        using T = std::decay_t<decltype(arg)>;
        
        if constexpr (std::is_same_v<T, NormalState>) {
            // 处理正常状态的转换
            if (c == '/') {
                next_states.push_back(LexerStateData::new_state(LineCommentState{true}));
                next_states.push_back(LexerStateData::new_state(BlockCommentState{BlockCommentState::State::WaitNextChar}));
                next_states.push_back(LexerStateData::new_state(OperatorState{OperatorState::Limit::NoSuffix}));
            } else if (c == '"') {
                next_states.push_back(LexerStateData::new_state(StringState{}));
            } else if (c == '\'') {
                next_states.push_back(LexerStateData::new_state(CharState{}));
            } else if (c == '`') {
                next_states.push_back(LexerStateData::new_state(RawIdentifierState{}));
            } else if (c == '(' || c == '[' || c == '{') {
                // 处理左括号
                state.nesting_state.push_bracket(c);
                TokenType bracket_type = (c == '(') ? TokenType::LeftParen :
                                       (c == '[') ? TokenType::LeftBracket : TokenType::LeftBrace;
                token = Token::make(bracket_type, std::u32string(1, c), state.nesting_state.get_nest_string());
                next_states.push_back(state.new_state(NormalState{false}));
            } else if (c == ')' || c == ']' || c == '}') {
                // 处理右括号
                if (!state.nesting_state.pop_bracket(c)) {
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::NestError, U"Mismatched bracket"}));
                } else {
                    TokenType bracket_type = (c == ')') ? TokenType::RightParen :
                                           (c == ']') ? TokenType::RightBracket : TokenType::RightBrace;
                    token = Token::make(bracket_type, std::u32string(1, c), state.nesting_state.get_nest_string());
                    next_states.push_back(state.new_state(NormalState{false}));
                }
            } else if (c == '!') {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(OperatorState{OperatorState::Limit::NoSuffix}));
                //next_states.push_back(LexerStateData::new_state(EnhancedBlockState{EnhancedBlockState::State::WaitStart}));
            }else if(is_prefix_operator(c)){
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(OperatorState{OperatorState::Limit::NoSuffix}));
            } else if (is_operator(c)) {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(OperatorState{OperatorState::Limit::BinaryOperatorOnly}));
            } else if (is_name_start(c)) {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(IdentifierState{}));
            } else if (is_digit(c)) {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(NumberState{}));
            } else if (is_whitespace(c)) {
                next_states.push_back(state.new_state(NormalState{false}));
            } else {
                next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::UnexpectedCharacter, U"Unexpected character"}));
            }
        }else if constexpr (std::is_same_v<T, LineCommentState>) {
            if (arg.wait_next_char) {
                if (c == '/') {
                    next_states.push_back(state.get_modified_state(LineCommentState{false}));
                }else{
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::UnexpectedCharacter, U"Unexpected character"}));
                }
            }else{
                if (c == '\n') {
                    state.state = NormalState{};
                    next_states.push_back(state);
                }else{
                    next_states.push_back(state);
                }
            }
        }else if constexpr (std::is_same_v<T, BlockCommentState>) {
            if (arg.state == BlockCommentState::State::WaitNextChar) {
                if (c == '*') {
                    next_states.push_back(state.get_modified_state(BlockCommentState{BlockCommentState::State::InComment}));
                }else{
                    next_states.push_back(state);
                }
            }else if (arg.state == BlockCommentState::State::InComment) {
                if (c == '*') {
                    next_states.push_back(state.get_modified_state(BlockCommentState{BlockCommentState::State::End}));
                    next_states.push_back(state);
                }else{
                    next_states.push_back(state);
                }
            }else if (arg.state == BlockCommentState::State::End) {
                if (c == '/') {
                    state.state = NormalState{};
                    next_states.push_back(state);
                }else{
                    next_states.push_back(state);
                }
            }
        }else if constexpr (std::is_same_v<T, StringState>) {
            if (arg.escape_text.size() > 0) {
                // 处理转义字符
                arg.escape_text += static_cast<char>(c);
                std::pair<EscapeCharState, char32_t> ch = escape_char(arg.escape_text);
                if (ch.first == EscapeCharState::Complete) {
                    state.accumulated_value += ch.second;
                    arg.escape_text.clear();
                    next_states.push_back(state);
                }else if (ch.first == EscapeCharState::InComplete) {
                    next_states.push_back(state);
                }else{
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::InvalidEscapeSequence, U"Invalid escape sequence"}));
                }
            } else if (c == '\\') {
                arg.escape_text += static_cast<char>(c);
                next_states.push_back(state);
            } else if (c == '"') {
                // 结束字符串
                token = Token::make(TokenType::String, state.accumulated_value, state.nesting_state.get_nest_string());
                state.state = NormalState{};
                next_states.push_back(state);
            }
            // 否则累积字符
            else {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state);
            }
        }else if constexpr (std::is_same_v<T, CharState>) {
            if (arg.escape_text.size() > 0) {
                // 处理转义字符
                arg.escape_text += static_cast<char>(c);
                std::pair<EscapeCharState, char32_t> ch = escape_char(arg.escape_text);
                if (ch.first == EscapeCharState::Complete) {
                    state.accumulated_value += ch.second;
                    arg.escape_text.clear();
                    next_states.push_back(state);
                }else if (ch.first == EscapeCharState::InComplete) {
                    next_states.push_back(state);
                }else{
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::InvalidEscapeSequence, U"Invalid escape sequence"}));
                }
            } else if (c == '\\') {
                arg.escape_text += static_cast<char>(c);
                next_states.push_back(state);
            } else if (c == '\'') {
                // 结束字符
                token = Token::make(TokenType::Char, state.accumulated_value, state.nesting_state.get_nest_string());
                state.state = NormalState{};
                next_states.push_back(state);
            } else {
                state.accumulated_value += static_cast<char>(c);
                if (state.accumulated_value.size() != 1) {
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::CharMustBeOneCharacter, U"Char must be one character"}));
                }else{
                    next_states.push_back(state);
                }
            }
        }else if constexpr (std::is_same_v<T, RawIdentifierState>) {
            if (c == '`') {
                token = Token::make(TokenType::RawIdentifier, state.accumulated_value, state.nesting_state.get_nest_string());
                state.state = NormalState{};
                next_states.push_back(state);
            }else if (c == '\n') {
                next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::RawIdentifierMustBeInOneLine, U"Raw identifier must be in one line"}));
            }else{
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state);
            }
        }else if constexpr (std::is_same_v<T, EnhancedBlockState>) {
            if (arg.state == EnhancedBlockState::State::WaitStart) {
                if (c == '\n') {
                    state.state = EnhancedBlockState{EnhancedBlockState::State::Start};
                    next_states.push_back(state);
                }else{
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::EnhancedBlockMustStartWithNewline, U"Enhanced block must start with newline"}));
                }
            } else if (arg.state == EnhancedBlockState::State::Start) {
                if (c == '\n') {
                    if (state.accumulated_value.size() >= 3 && state.accumulated_value.size() <= 120) {
                        state.state = EnhancedBlockState{EnhancedBlockState::State::InContent, U"\n" + state.accumulated_value};
                        state.accumulated_value = U"";
                        next_states.push_back(state);
                    }else{
                        next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::EnhancedBlockDelimiterMustBe3To120Characters, U"Enhanced block delimiter must be 3-120 characters"}));
                    }
                }else if (is_delimiter_char(c)) {
                    state.accumulated_value += static_cast<char>(c);
                    next_states.push_back(state);
                }else {
                    next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::EnhancedBlockDelimiterMustConsistOfDashOrEqual, U"Enhanced block delimiter must be consist of '-' or '='"}));
                }
            } else if (arg.state == EnhancedBlockState::State::InContent) {
                if (c == '\n') {
                    if (string_end_of(state.accumulated_value, arg.delimiter)) {
                        state.accumulated_value.erase(state.accumulated_value.size() - arg.delimiter.size(), arg.delimiter.size());
                        token = Token::make(TokenType::BlockContent, state.accumulated_value, state.nesting_state.get_nest_string());
                        next_states.push_back(state.new_state(NormalState{true}));
                    }else{
                        state.accumulated_value += static_cast<char>(c);
                        next_states.push_back(state);
                    }
                }else {
                    state.accumulated_value += static_cast<char>(c);
                    next_states.push_back(state);
                }
            }else{
                next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::UnexpectedEnhancedBlockState, U"Unexpected Enhanced block state"}));
            }
        }else if constexpr (std::is_same_v<T, IdentifierState>) {
            if (is_operator(c)) {
                token = Token::make(TokenType::Identifier, state.accumulated_value, state.nesting_state.get_nest_string());
                state.accumulated_value.clear();
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state.get_modified_state(OperatorState{OperatorState::Limit::NoPrefix}));

            }else if (is_name_char(c)) {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state);
            }else{
                token = Token::make(TokenType::Identifier, state.accumulated_value, state.nesting_state.get_nest_string());
                next_states.push_back(state.new_state(NormalState{true}));
            }
        }else if constexpr (std::is_same_v<T, OperatorState>) {
            if (is_operator(c)) {
                if (!is_prefix_operator(c)) {
                    // a += b
                    //    ^ Here
                    state.state=OperatorState{OperatorState::Limit::BinaryOperatorOnly};
                }
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state);
            }else if (is_name_char(c)) {
                if (arg.limit == OperatorState::Limit::NoPrefix) {
                    // a$b - 二元操作符后紧跟标识符，这违反了空格要求，但出于对常用写法的兼容，进行处理
                    //   ^ Here
                    token = Token::make(TokenType::BinaryOperator, state.accumulated_value, state.nesting_state.get_nest_string());
                    next_states.push_back(state.new_state(NormalState{true}));
                }else if (arg.limit == OperatorState::Limit::NoSuffix) {
                    // ++a
                    //   ^ Here
                    token = Token::make(TokenType::PrefixOperator, state.accumulated_value, state.nesting_state.get_nest_string());
                    next_states.push_back(state.new_state(NormalState{true}));
                }else if (arg.limit == OperatorState::Limit::BinaryOperatorOnly) {
                    // a @op b
                    //    ^ Here
                    state.accumulated_value += static_cast<char>(c);
                    next_states.push_back(state);
                }
            }else{
                if (c == '\n')
                { 
                    assert(state.accumulated_value.size() > 0);
                    if (state.accumulated_value.back() == U'!') {
                        state.accumulated_value.pop_back();
                        next_states.push_back(state.new_state(EnhancedBlockState{EnhancedBlockState::State::Start}));
                    }else{
                        next_states.push_back(state.new_state(NormalState{true}));
                    }
                }else{
                    next_states.push_back(state.new_state(NormalState{true}));
                }
                if (!state.accumulated_value.empty()) {
                    if (arg.limit == OperatorState::Limit::NoPrefix) {
                        // a++
                        //    ^ Here
                        token = Token::make(TokenType::SuffixOperator, state.accumulated_value, state.nesting_state.get_nest_string());
                    }else{
                        // a % b
                        //    ^ Here
                        token = Token::make(TokenType::BinaryOperator, state.accumulated_value, state.nesting_state.get_nest_string());
                    }
                }
            }
        }else if constexpr (std::is_same_v<T, NumberState>) {
            if (is_name_char(c) || c == '.' || c == '-' || c == '+' || c == '_') {
                state.accumulated_value += static_cast<char>(c);
                next_states.push_back(state);
            }else{
                token = Token::make(TokenType::Number, state.accumulated_value, state.nesting_state.get_nest_string());
                next_states.push_back(state.new_state(NormalState{true}));
            }
        }else if constexpr (std::is_same_v<T, ErrorState>) {
            // 错误状态：跳过当前字符，回到正常状态继续处理
            // 不产生新的ErrorState，直接回到NormalState
            next_states.push_back(state.new_state(NormalState{false}));
        }else {
            next_states.push_back(LexerStateData::new_state(ErrorState{LexerErrorType::UnexpectedState, U"Unexpected state"}));
        }
    }, state.state);
    
    return {token, next_states};
}

inline bool LexerState::is_name_start(char32_t c) {
    if (c == U'_'
        || (c >= U'a' && c <= U'z')
        || (c >= U'A' && c <= U'Z')) {
        return true;
    }
    return false;
}

inline bool LexerState::is_name_char(char32_t c) {
    if (is_name_start(c) || (c >= U'0' && c <= U'9')) {
        return true;
    }
    return false;
}

inline bool LexerState::is_delimiter_char(char32_t c) {
    if (c == U'-' || c == U'=') {
        return true;
    }
    return false;
}

inline bool LexerState::is_whitespace(char32_t c) {
    if (c == U' ' || c == U'\t' || c == U'\n' || c == U'\r') {
        return true;
    }
    return false;
}

inline bool LexerState::is_digit(char32_t c) {
    if (c >= U'0' && c <= U'9') {
        return true;
    }
    return false;
}

inline bool LexerState::is_prefix_operator(char32_t c) {
    if (c == U'+' || c == U'-' || c == U'!' || c == U'~') {
        return true;
    }
    return false;
}

inline bool LexerState::is_other_operator(char32_t c) {
    if (c == U'.' || c == U'+' || c == U'-' || c == U'*' || c == U'/' || c == U'%') {
        return false;
    }
    if (is_name_char(c)) {
        return false;
    }
    if (is_whitespace(c)) {
        return false;
    }
    if (c == U'[' || c == U']' || c == U'(' || c == U')' || c == U'{' || c == U'}') {
        return false;
    }
    auto category = u_charType(c);
    if (U_MASK(category) & (U_GC_SM_MASK | U_GC_SC_MASK | U_GC_PO_MASK | U_GC_PD_MASK)) {
        return true;
    }
    return false;
}

inline bool LexerState::is_operator(char32_t c) {
    if (c == U'.' || c == U'@' || c == U'+' || c == U'-' || c == U'*' || c == U'/' || c == U'%') {
        return true;
    }
    if (is_prefix_operator(c) || is_other_operator(c)) {
        return true;
    }
    return false;
}

inline std::pair<LexerState::EscapeCharState, char32_t> LexerState::escape_char(const std::u32string& escape_text) {
    if (escape_text.size() == 1) {
        if (escape_text[0] == U'\\') {
            return {EscapeCharState::InComplete, 0};
        }else{
            return {EscapeCharState::Complete, escape_text[0]};
        }
    }else if (escape_text.size() == 2) {
        if (escape_text[0] == U'\\') {
            if (escape_text[1] == U'n') {
                return {EscapeCharState::Complete, U'\n'};
            }else if (escape_text[1] == U't') {
                return {EscapeCharState::Complete, U'\t'};
            }else if (escape_text[1] == U'r') {
                return {EscapeCharState::Complete, U'\r'};
            }else if (escape_text[1] == U'\\') {
                return {EscapeCharState::Complete, U'\\'};
            }else if (escape_text[1] == U'\'') {
                return {EscapeCharState::Complete, U'\''};
            }else if (escape_text[1] == U'"') {
                return {EscapeCharState::Complete, U'"'};
            }else if (escape_text[1] == U'b') {
                return {EscapeCharState::Complete, U'\b'};
            }else if (escape_text[1] == U'f') {
                return {EscapeCharState::Complete, U'\f'};
            }else if (escape_text[1] == U'a') {
                return {EscapeCharState::Complete, U'\a'};
            }else if (escape_text[1] == U'v') {
                return {EscapeCharState::Complete, U'\v'};
            // }else if (escape_text[1] == U'e') {
            //     return {EscapeCharState::Complete, U'\e'};
            }else if (escape_text[1] == U'0') {
                return {EscapeCharState::Complete, U'\0'};
            }else{
                return {EscapeCharState::Error, 0};
            }
        }else{
            return {EscapeCharState::Complete, escape_text[1]};
        }
    }else{
        return {EscapeCharState::Error, 0};
    }
}

inline bool LexerState::string_end_of(const std::u32string& data, const std::u32string& wanted) {
    if (data.size() < wanted.size()) {
        return false;
    }
    for (size_t i = 0; i < wanted.size(); i++) {
        if (data[data.size() - wanted.size() + i] != wanted[i]) {
            return false;
        }
    }
    return true;
}
