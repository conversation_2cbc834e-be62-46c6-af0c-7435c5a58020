#include <fstream>
#include <iostream>
#include <string>
#include <map>
#include <list>
#include <cassert>
#include <locale>
#include "../text.h"
#include "../lexer.h"


std::string u32string_to_string(const std::u32string& u32str) {
    char* utf8 = NULL;
    int len = Text::char32ToUtf8(u32str.c_str(), u32str.size(), &utf8);
    if (len <= 0) return "";
    std::string res(utf8);
    free(utf8);
    return res;
}

// 全局错误收集器
LexerErrorCollector g_error_collector;

void LexerErrorCollector::add_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    int line = info ? info->line : 0;
    int column = info ? info->column : 0;
    errors.emplace_back(type, message, line, column);
}

void LexerErrorCollector::add_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    int line = info ? info->line : 0;
    int column = info ? info->column : 0;
    warnings.emplace_back(type, message, line, column);
}

void generate_error(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Error at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Error: %s\n", message_str.c_str());
    }
}

void generate_warning(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Warning at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Warning: %s\n", message_str.c_str());
    }
}

void generate_structured_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    g_error_collector.add_error(type, message, info);
    generate_error(message, info);
}

void generate_structured_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    g_error_collector.add_warning(type, message, info);
    generate_warning(message, info);
}

struct ErrorStateExpectation {
    int line;
    int column;
    std::vector<LexerErrorType> error_types;
};

struct TestData {
    std::u32string input;
    std::vector<std::u32string> expected_tokens;
    std::vector<TokenType> expected_types;
    std::vector<ErrorStateExpectation> expected_errors;
};

static std::vector<std::u32string> split_by(const std::u32string& input, char32_t split_char) {
    std::vector<std::u32string> res;
    std::u32string current;
    for (auto c : input) {
        if (c == split_char) {
            res.push_back(current);
            current.clear();
        }else{
            current += c;
        }
    }
    if (current.size() > 0) {
        res.push_back(current);
    }
    return res;
}

static TokenType token_type_from_string(const std::u32string& type_string) {
    if (type_string == U"Identifier") return TokenType::Identifier;
    if (type_string == U"RawIdentifier") return TokenType::RawIdentifier;
    if (type_string == U"Number") return TokenType::Number;
    if (type_string == U"String") return TokenType::String;
    if (type_string == U"Char") return TokenType::Char;
    if (type_string == U"LineComment") return TokenType::LineComment;
    if (type_string == U"BlockComment") return TokenType::BlockComment;
    if (type_string == U"BinaryOperator") return TokenType::BinaryOperator;
    if (type_string == U"PrefixOperator") return TokenType::PrefixOperator;
    if (type_string == U"SuffixOperator") return TokenType::SuffixOperator;
    if (type_string == U"Keyword") return TokenType::Keyword;
    if (type_string == U"Punctuation") return TokenType::Punctuation;
    if (type_string == U"Whitespace") return TokenType::Whitespace;
    if (type_string == U"BlockContent") return TokenType::BlockContent;
    if (type_string == U"LeftParen") return TokenType::LeftParen;
    if (type_string == U"RightParen") return TokenType::RightParen;
    if (type_string == U"LeftBracket") return TokenType::LeftBracket;
    if (type_string == U"RightBracket") return TokenType::RightBracket;
    if (type_string == U"LeftBrace") return TokenType::LeftBrace;
    if (type_string == U"RightBrace") return TokenType::RightBrace;
    return TokenType::Identifier;
}

static std::u32string token_type_to_string(TokenType type) {
    switch (type) {
        case TokenType::Identifier: return U"Identifier";
        case TokenType::RawIdentifier: return U"RawIdentifier";
        case TokenType::Number: return U"Number";
        case TokenType::String: return U"String";
        case TokenType::Char: return U"Char";
        case TokenType::LineComment: return U"LineComment";
        case TokenType::BlockComment: return U"BlockComment";
        case TokenType::BinaryOperator: return U"BinaryOperator";
        case TokenType::PrefixOperator: return U"PrefixOperator";
        case TokenType::SuffixOperator: return U"SuffixOperator";
        case TokenType::Keyword: return U"Keyword";
        case TokenType::Punctuation: return U"Punctuation";
        case TokenType::Whitespace: return U"Whitespace";
        case TokenType::BlockContent: return U"BlockContent";
        case TokenType::LeftParen: return U"LeftParen";
        case TokenType::RightParen: return U"RightParen";
        case TokenType::LeftBracket: return U"LeftBracket";
        case TokenType::RightBracket: return U"RightBracket";
        case TokenType::LeftBrace: return U"LeftBrace";
        case TokenType::RightBrace: return U"RightBrace";
    }
    return U"Unknown";
}

static LexerErrorType error_type_from_string(const std::u32string& error_string) {
    if (error_string == U"unexpected_character") return LexerErrorType::UnexpectedCharacter;
    if (error_string == U"lexer_inject") return LexerErrorType::LexerInject;
    if (error_string == U"invalid_escape_sequence") return LexerErrorType::InvalidEscapeSequence;
    if (error_string == U"char_must_be_one_character") return LexerErrorType::CharMustBeOneCharacter;
    if (error_string == U"raw_identifier_must_be_in_one_line") return LexerErrorType::RawIdentifierMustBeInOneLine;
    if (error_string == U"enhanced_block_must_start_with_newline") return LexerErrorType::EnhancedBlockMustStartWithNewline;
    if (error_string == U"enhanced_block_delimiter_must_be_3_to_120_characters") return LexerErrorType::EnhancedBlockDelimiterMustBe3To120Characters;
    if (error_string == U"enhanced_block_delimiter_must_consist_of_dash_or_equal") return LexerErrorType::EnhancedBlockDelimiterMustConsistOfDashOrEqual;
    if (error_string == U"unexpected_enhanced_block_state") return LexerErrorType::UnexpectedEnhancedBlockState;
    if (error_string == U"unexpected_state") return LexerErrorType::UnexpectedState;
    if (error_string == U"nest_error") return LexerErrorType::NestError;
    if (error_string == U"tokenization_rejected_by_all_states") return LexerErrorType::TokenizationRejectedByAllStates;
    if (error_string == U"tokenization_accepted_by_multiple_states") return LexerErrorType::TokenizationAcceptedByMultipleStates;
    if (error_string == U"multiple_states_after_finalization") return LexerErrorType::MultipleStatesAfterFinalization;
    if (error_string == U"no_state_after_finalization") return LexerErrorType::NoStateAfterFinalization;
    if (error_string == U"not_in_normal_state_after_finalization") return LexerErrorType::NotInNormalStateAfterFinalization;
    return LexerErrorType::UnexpectedState;
}

void parse_expected_tokens(std::vector<std::u32string>& out_tokens,std::vector<TokenType>& out_types, const std::u32string& input) {
    auto lines = split_by(input, '\n');
    enum class ReadState{
        WaitType, WaitValue
    } read_state = ReadState::WaitType;
    std::u32string token_type_string, token_value_string;
    for(auto const& line : lines) {
        if (read_state == ReadState::WaitType) {
            if (line.size() <= 2) continue;
            if (line.front() != U'+' || line.back() != U'+') continue;
            token_type_string = line.substr(1, line.size() - 2);
            read_state = ReadState::WaitValue;
        }else if (read_state == ReadState::WaitValue) {
            if (line.size() <= 2) {
                token_value_string += line;
                continue;
            }else if (line.front() == U'+' && line.back() == U'+') {
                out_tokens.push_back(token_value_string);
                token_value_string.clear();
                out_types.push_back(token_type_from_string(token_type_string));
                token_type_string = line.substr(1, line.size() - 2);
                read_state = ReadState::WaitValue;
            }else{
                token_value_string += line;
            }
        }
    }
    if (token_value_string.size() > 0) {
        out_tokens.push_back(token_value_string);
        out_types.push_back(token_type_from_string(token_type_string));
    }
}

void parse_expected_errors(std::vector<ErrorStateExpectation>& out_errors, const std::u32string& input) {
    auto lines = split_by(input, '\n');
    ErrorStateExpectation current_error;
    bool reading_position = true;

    for(auto const& line : lines) {
        if (line.empty()) continue;

        if (reading_position) {
            // 解析行号:列号格式
            auto colon_pos = line.find(U':');
            if (colon_pos != std::u32string::npos) {
                std::u32string line_str = line.substr(0, colon_pos);
                std::u32string col_str = line.substr(colon_pos + 1);

                // 转换为整数
                current_error.line = std::stoi(u32string_to_string(line_str));
                current_error.column = std::stoi(u32string_to_string(col_str));
                current_error.error_types.clear();
                reading_position = false;
            }
        } else {
            // 解析错误类型
            if (!line.empty()) {
                current_error.error_types.push_back(error_type_from_string(line));
            }
        }
    }

    if (!reading_position && !current_error.error_types.empty()) {
        out_errors.push_back(current_error);
    }
}

std::list<TestData> read_test_data(const LexerState& lexer) {
    auto tokens = lexer.get_tokens();
    std::list<TestData> res;
    std::map<std::u32string, TestData> test_data_map; // 用于收集同一测试的不同部分

    if (tokens.size() <= 1) return res;

    // 寻找块结构：block <name> : <type>! 或 block <name> { ... }
    for (auto it = tokens.begin(); it != tokens.end(); it++) {
        std::shared_ptr<Token> token = *it;
        if (token->type == TokenType::Identifier && token->value == U"block") {
            // 找到block关键字，寻找后续的结构
            auto name_it = std::next(it);
            if (name_it != tokens.end() && (*name_it)->type == TokenType::Identifier) {
                std::u32string block_name = (*name_it)->value;

                // 检查是否是大括号格式：block name { ... }
                auto next_it = std::next(name_it);
                if (next_it != tokens.end() && (*next_it)->type == TokenType::LeftBrace) {
                    // 这是一个测试组，跳过到右大括号
                    int brace_count = 1;
                    auto brace_it = std::next(next_it);
                    while (brace_it != tokens.end() && brace_count > 0) {
                        if ((*brace_it)->type == TokenType::LeftBrace) {
                            brace_count++;
                        } else if ((*brace_it)->type == TokenType::RightBrace) {
                            brace_count--;
                        }
                        if (brace_count > 0) {
                            brace_it++;
                        }
                    }
                    if (brace_count == 0) {
                        it = brace_it; // 跳过整个大括号块
                    }
                    continue;
                }

                // 寻找冒号（原有格式）
                auto colon_it = next_it;
                if (colon_it != tokens.end() && (*colon_it)->type == TokenType::BinaryOperator && (*colon_it)->value == U":") {

                    // 寻找块类型
                    auto type_it = std::next(colon_it);
                    if (type_it != tokens.end() && (*type_it)->type == TokenType::Identifier) {
                        std::u32string block_type = (*type_it)->value;

                        // 寻找块内容（可能有感叹号，也可能没有）
                        auto content_it = std::next(type_it);

                        // 检查是否有感叹号
                        if (content_it != tokens.end() &&
                            ((*content_it)->type == TokenType::SuffixOperator || (*content_it)->type == TokenType::PrefixOperator) &&
                            (*content_it)->value == U"!") {
                            content_it = std::next(content_it);
                        }

                        if (content_it != tokens.end() && (*content_it)->type == TokenType::BlockContent) {
                            std::u32string content = (*content_it)->value;

                            // 提取测试名称（去掉后缀）
                            std::u32string test_name = block_name;
                            if (block_name.find(U"_result") != std::u32string::npos) {
                                test_name = block_name.substr(0, block_name.find(U"_result"));
                            } else if (block_name.find(U"_error") != std::u32string::npos) {
                                test_name = block_name.substr(0, block_name.find(U"_error"));
                            }

                            if (block_type == U"_lexer_input") {
                                test_data_map[test_name].input = content;
                            }else if (block_type == U"_lexer_expected") {
                                parse_expected_tokens(test_data_map[test_name].expected_tokens, test_data_map[test_name].expected_types, content);
                            }else if (block_type == U"_lexer_error_state") {
                                parse_expected_errors(test_data_map[test_name].expected_errors, content);
                            }else if (block_type == U"text") {
                                // 忽略文档块
                            }

                            it = content_it; // 跳过已处理的token
                        }
                    }
                }
            }
        }
    }

    // 将收集到的测试数据转换为列表
    for (auto& pair : test_data_map) {
        if (pair.second.input.size() > 0) {
            res.push_back(pair.second);
        }
    }

    return res;
}

bool run_single_test(const TestData& test_data) {
    // 清空错误收集器
    g_error_collector.clear();



    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : test_data.input) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);
    auto tokens = lexer.get_tokens();

    bool has_error = false;

    // 验证token输出（如果有期望的token）
    if (!test_data.expected_tokens.empty()) {
        auto output_it = tokens.begin();
        auto expected_value_it = test_data.expected_tokens.begin();
        auto expected_type_it = test_data.expected_types.begin();

        while (output_it != tokens.end() && expected_value_it != test_data.expected_tokens.end() && expected_type_it != test_data.expected_types.end()) {
            if ((*output_it)->value != *expected_value_it) {
                generate_error(U"Expected token value: " + *expected_value_it + U", but got: " + (*output_it)->value, nullptr);
                has_error = true;
                break;
            }
            if ((*output_it)->type != *expected_type_it) {
                generate_error(U"Expected token type: " + token_type_to_string(*expected_type_it) + U", but got: " + token_type_to_string((*output_it)->type), nullptr);
                has_error = true;
                break;
            }
            output_it++;
            expected_value_it++;
            expected_type_it++;
        }
        if (output_it != tokens.end()) {
            generate_error(U"Extra tokens: " + (*output_it)->value, nullptr);
            has_error = true;
        }
        if (expected_value_it != test_data.expected_tokens.end()) {
            generate_error(U"Missing tokens: " + *expected_value_it, nullptr);
            has_error = true;
        }
    }

    // 验证错误状态（如果有期望的错误）
    if (!test_data.expected_errors.empty()) {
        for (const auto& expected_error : test_data.expected_errors) {
            bool found_matching_error = false;
            for (const auto& actual_error : g_error_collector.errors) {
                if (actual_error.line == expected_error.line && actual_error.column == expected_error.column) {
                    // 检查是否包含期望的错误类型
                    for (const auto& expected_type : expected_error.error_types) {
                        if (actual_error.type == expected_type) {
                            found_matching_error = true;
                            break;
                        }
                    }
                    if (found_matching_error) break;
                }
            }
            if (!found_matching_error) {
                std::cout << "Expected error at " << expected_error.line << ":" << expected_error.column << " not found" << std::endl;
                has_error = true;
            }
        }
    }

    if (has_error) {
        std::cout << "Test failed: " << u32string_to_string(test_data.input) << std::endl;
        std::cout << "Output: " << std::endl;
        for (auto& token : tokens) {
            std::cout << "    " << u32string_to_string(token->value) << ":" << u32string_to_string(token_type_to_string(token->type)) << std::endl;
        }
        if (!g_error_collector.errors.empty()) {
            std::cout << "Errors: " << std::endl;
            for (const auto& error : g_error_collector.errors) {
                std::cout << "    " << error.line << ":" << error.column << " - " << u32string_to_string(error.message) << std::endl;
            }
        }
        std::cout << std::endl;
    }
    return !has_error;
}

bool run_tests(std::list<TestData>& test_data) {
    bool res = true;
    std::cout << "Starting tests..." << std::endl;
    for (auto& data : test_data) {
        std::cout << "Running test: " << u32string_to_string(data.input) << std::endl;
        res &= run_single_test(data);
    }
    return res;
}

int main() {
    setlocale(LC_ALL, "en_US.utf8");
    std::fstream file("src/llvm-frontend/tests/lexer.lddk");
    if (!file.is_open()) {
        std::cout << "Failed to open test file" << std::endl;
        return -1;
    }
    std::string content((std::istreambuf_iterator<char>(file)),
        std::istreambuf_iterator<char>());
    char32_t *u32 = NULL;
    int len = Text::utf8ToChar32(content.c_str(), content.size(), &u32);
    if (len <= 0)
    {
        std::cout << "Failed to convert to UTF-32" << std::endl;
        return -1;
    }
    std::u32string u32str(u32, u32 + len - 1);
    free(u32);
    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : u32str) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);

    auto test_data = read_test_data(lexer);
    if (test_data.empty()) {
        std::cout << "No tests found" << std::endl;
        return -1;
    }

    if (run_tests(test_data)) {
        std::cout << "All tests passed" << std::endl;
    }else{
        std::cout << "Some tests failed" << std::endl;
    }
    return 0;
}
