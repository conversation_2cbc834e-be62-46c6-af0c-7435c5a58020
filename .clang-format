# 默认设置
BasedOnStyle: LLVM
# 应用到C/C++
Language: Cpp
# 2.4.2 缩进与对齐
#  在大部分的程序块中，使用制表符进行代码缩进对齐，制表符的大小应定义为 4，换言之，缩进
#  必须是 4 的整数倍。函数体、程序块、case 语句块、结构体等类型定义中都要使用缩进。
IndentWidth: 4
TabWidth: 4
#  根据具体情况，使用制表符（Tab）或者空格来使代码对齐。
#  目前设置为行首尾使用Tab,其他时候用空格
#  可能的值：
#   Never: Never use tab.
#   ForIndentation: Use tabs only for indentation.
#   ForContinuationAndIndentation: Fill all leading whitespace with tabs,
#     and use spaces for alignment that appears within a line 
#     (e.g. consecutive assignments and declarations).
#   AlignWithSpaces: Use tabs for line continuation and indentation, 
#     and spaces for alignment.
#   Always: Use tabs whenever we need to fill whitespace that spans at 
#     least from one tab stop to the next one.
UseTab: ForContinuationAndIndentation
#  case 语句较为特殊，case 要和上层的 switch 左对齐。
IndentCaseLabels: false
IndentCaseBlocks: true
#  多个语句或者注释拥有同等的地位或者再完成同类的事情，对于这样的语句和注释，遵循美观的原则，
#  可用空格进行一定的调整，使其看起来尽可能整齐些，这样容易让阅读者区分同类操作的范围。
#  目前设置为自动对齐连续的多行赋值语句，中间的注释行不影响，只有空行影响，这里可以按个人习惯修改
#  可能的值：None/Consecutive/AcrossEmptyLines/AcrossComments/AcrossEmptyLinesAndComments
AlignConsecutiveAssignments: AcrossComments 
AlignConsecutiveBitFields: AcrossComments # 位域同理
AlignConsecutiveDeclarations: AcrossComments # 声明同理
AlignConsecutiveMacros: AcrossComments # 宏同理

#   其他设置
AlignArrayOfStructures: Left 
ContinuationIndentWidth: 4
DerivePointerAlignment: false
IndentGotoLabels: false
#   预处理语句缩进，可选值：
#     None/AfterHash/BeforeHash
IndentPPDirectives: AfterHash
IndentWrappedFunctionNames: true
QualifierAlignment: Leave
#   过长注释自动换行
ReflowComments: true
# 2.4.3 程序块
#   程序块有一对大括号标示，每个大括号应另起一行，同对应的大括号同处一列，且与引用该程序
#   块的语句/关键字左对齐。
BreakBeforeBraces: Custom
BraceWrapping:
  AfterCaseLabel: true
  AfterClass: true
  AfterControlStatement: Always 
  AfterEnum: true
  AfterFunction: true
  AfterNamespace: true
  AfterStruct: true
  AfterUnion: true
  AfterExternBlock: true
  BeforeCatch: false
  BeforeElse: false
  BeforeLambdaBody: false
  BeforeWhile: false # do-while结构不处理while
  SplitEmptyFunction: false
  SplitEmptyNamespace: false
AllowShortBlocksOnASingleLine: Always
AllowShortIfStatementsOnASingleLine: AllIfsAndElse
AllowShortLoopsOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: true
AllowShortEnumsOnASingleLine: false
AllowShortFunctionsOnASingleLine: Inline
#   
InsertBraces: false

# 2.4.4 换行
#   每行代码的最大长度为 100 个字符
ColumnLimit: 100
#   非赋值语句整体过长，换行位置的选取根据表达式的逻辑关系，尽量选择在低优先级操作符处进
#   行换行，同时遵循美观的基本原则。
BreakBeforeBinaryOperators: All
#   当赋值语句的右侧过长时，换行优先选择低优先级操作符处，换行后操作符放在行首，缩进应与
#   首行’=’右侧首字母靠近。
#   当赋值语句的左侧过长时，应从’=’起换行，换行位置的选取遵循美观的原则。
AlignOperands: Align
BreakBeforeTernaryOperators: true
#   函数声明或者调用都有可能发生参数列表过长的情况，在函数声明时，必须统一成第一个参数与
#   函数声明同行，从第二个参数开始、每行一个的格式，第二个参数开始和第一个参数左对齐；在函数
#   调用时，则可根据具体情况，选择每行一个参数或者长参数一行、多个段参数一行的格式，同样的，
#   第二行开始的参数需要和第一个参数左对齐。
BinPackArguments : false
BinPackParameters: false
AlignAfterOpenBracket: Align 
AllowAllArgumentsOnNextLine: false
AllowAllParametersOfDeclarationOnNextLine: false
#   其他设置
AlignEscapedNewlines: Right 
AlwaysBreakBeforeMultilineStrings: false
IncludeBlocks: Preserve
KeepEmptyLinesAtTheStartOfBlocks: false

#2.4.5 空格/空行
#   在逻辑运算符的前后应该有且仅有一个 ASCII 空格字符
SpaceBeforeAssignmentOperators: true
#   在前/后缀一元运算符及其操作之间不能有空白
#   单目运算符、指针操作符等前后不加空格
SpaceAfterLogicalNot: false
SpaceAfterCStyleCast: false
SpaceBeforeSquareBrackets: false
#   解引用操作符 '*' 和地址操作符 `&' 与类型名之间加空格，与变量名之间无空格
PointerAlignment: Right
#   如果想在形如void *const *x = NULL;的const前加空格可以设置为After
SpaceAroundPointerQualifiers: Default
#   if、for、while、switch 等关键字后加空格
SpaceBeforeParens: ControlStatements
#   其他设置
SpaceBeforeCaseColon: false
SpacesInSquareBrackets: false
SpaceInEmptyBlock: false
SpacesInLineCommentPrefix:
  Minimum: 1
  Maximum: -1
BitFieldColonSpacing: Both

#C++
Standard: c++14
AccessModifierOffset: -4
AllowShortLambdasOnASingleLine: None
BreakBeforeConceptDeclarations: Allowed
BreakConstructorInitializers: BeforeComma
BreakInheritanceList: BeforeComma
BreakStringLiterals: true
CompactNamespaces: true
EmptyLineAfterAccessModifier: Leave
EmptyLineBeforeAccessModifier: Leave
FixNamespaceComments: true
IndentAccessModifiers: false
IndentExternBlock: NoIndent
IndentRequiresClause: true
LambdaBodyIndentation: Signature
NamespaceIndentation: Inner
PackConstructorInitializers: BinPack
SpaceAfterTemplateKeyword: false
SpaceBeforeCpp11BracedList: true
SpaceBeforeCtorInitializerColon: false
SpaceBeforeInheritanceColon: false
SpaceBeforeRangeBasedForLoopColon: true
SpacesInAngles: false
Cpp11BracedListStyle: true