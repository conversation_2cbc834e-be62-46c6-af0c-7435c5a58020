# Lexer增强

## 结构化错误处理（部分完成）

目前的错误处理通过硬编码的字符串实现，导致测试程序无从下手。
希望：

1. 优化错误处理，传递结构化的数据到上层
2. 增加错误状态的测试，即检测输入某个字符串之后是否到达指定的错误状态。
   * （基本完成）测试程序需要增加支持，引入_lexer_error_state块来表达错误状态
   * （部分代码完成，但无法通过测试用例）测试用例组织调整大括号包含的块，一个块为一个测试用例，块内包含一个_lexer_input块、一个_lexer_expected块和任意数量的_lexer_error_state块
   * （仍有问题）需要先增加大括号处理逻辑才能调整测试用例的组织
   * （基本完成）_lexer_error_state块内第一行为行号:列号的形式，之后的每一行代表一个错误状态
   * 多测试用例文件支持
   * Lexer运行到某个位置后，可能包含多个错误状态

一个测试用例示例（仅作为格式参考）：
```
block error1 {
block in1 : _lexer_input !
========
人 a
========

block res1 : _lexer_expected !
========
+Identifier+
a
========

block err1 : _lexer_error_state !
========
1:1
unexpected_character
lexer_inject
========

}

```

## 语法支持完善

目前的实现没有完成下面的功能：
1. （没有处理）增强块的`<indent_string>`没有增加支持，参考文档：[baselanguage_design.md](baselanguage_design.md#ebnf定义)
2. （部分完成）希望词法分析阶段能处理括号嵌套，并检测括号嵌套错误
   * Token数据结构增加`nest_state`的字符串，表达括号的嵌套状态
   * 增加`(` `)` `[` `]` `{` `}`的token类型（或许一个类型就行？）
   * 增加`nest_error`的错误类型
3. 仍有部分测试用例无法通过，先修复测试用例

## 整体要求

保持简洁，尽量单文件内完成所需的逻辑。
<!-- 测试用例编译方法：运行windows_build.bat
运行测试方法：`."D:/git/LDDK/build/Debug/lexer_test.exe"` -->
测试用例编译方法：`./build.sh`
运行测试方法：`./build/lexer_test`